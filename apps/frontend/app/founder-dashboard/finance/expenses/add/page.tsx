'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  TrendingDown,
  ArrowLeft,
  Save,
  Loader2,
  DollarSign,
  Calendar,
  FileText,
  Building,
  Users,
  Tag
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { financeAPI, projectsAPI, usersAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';

// Expense types and statuses
const expenseTypes = [
  { value: 'operational', label: 'تشغيلي' },
  { value: 'capital', label: 'رأسمالي' },
  { value: 'administrative', label: 'إداري' },
  { value: 'marketing', label: 'تسويقي' },
  { value: 'travel', label: 'سفر' },
  { value: 'equipment', label: 'معدات' },
  { value: 'software', label: 'برمجيات' },
  { value: 'training', label: 'تدريب' },
  { value: 'maintenance', label: 'صيانة' },
  { value: 'other', label: 'أخرى' }
];

const expenseStatuses = [
  { value: 'pending', label: 'في الانتظار' },
  { value: 'approved', label: 'موافق عليه' },
  { value: 'rejected', label: 'مرفوض' },
  { value: 'paid', label: 'مدفوع' }
];

export default function AddExpensePage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  
  // Form data
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: '',
    category: null,
    status: 'pending',
    amount: '',
    tax_amount: '0',
    expense_date: new Date().toISOString().split('T')[0],
    due_date: '',
    team_member: null,
    project: null,
    receipt_number: '',
    vendor_name: '',
    notes: ''
  });

  // Data for dropdowns
  const [categories, setCategories] = useState([]);
  const [projects, setProjects] = useState([]);
  const [teamMembers, setTeamMembers] = useState([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchInitialData();
    }
  }, [mounted, isAuthenticated]);

  const fetchInitialData = async () => {
    try {
      setInitialLoading(true);

      // Fetch categories, projects, and team members in parallel
      const [categoriesResponse, projectsResponse, usersResponse] = await Promise.all([
        financeAPI.getExpenseCategories(),
        projectsAPI.getProjects({ page_size: 100 }),
        usersAPI.getUsers({ page_size: 100 })
      ]);

      setCategories(categoriesResponse.results || categoriesResponse);
      setProjects(projectsResponse.results || projectsResponse);
      setTeamMembers(usersResponse.results || usersResponse);

    } catch (error) {
      console.error('Error fetching initial data:', error);
      showToast.error('فشل في تحميل البيانات الأولية');
    } finally {
      setInitialLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      showToast.error('يرجى إدخال عنوان المصروف');
      return;
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      showToast.error('يرجى إدخال مبلغ صحيح');
      return;
    }

    if (!formData.type) {
      showToast.error('يرجى اختيار نوع المصروف');
      return;
    }

    try {
      setLoading(true);

      // Prepare data for API
      const submitData = {
        ...formData,
        category: formData.category && formData.category !== 'none' ? formData.category : null,
        project: formData.project && formData.project !== 'none' ? formData.project : null,
        team_member: formData.team_member && formData.team_member !== 'none' ? formData.team_member : null,
        amount: parseFloat(formData.amount),
        tax_amount: parseFloat(formData.tax_amount) || 0,
        due_date: formData.due_date || null
      };

      await financeAPI.createExpense(submitData);
      
      showToast.success('تم إضافة المصروف بنجاح');
      router.push('/founder-dashboard/finance/expenses');
      
    } catch (error) {
      console.error('Error creating expense:', error);
      showToast.error('فشل في إضافة المصروف');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/founder-dashboard/finance/expenses');
  };

  if (!mounted || !isAuthenticated) {
    return null;
  }

  if (initialLoading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            العودة
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <TrendingDown className="h-8 w-8 text-red-600" />
              إضافة مصروف جديد
            </h1>
            <p className="text-gray-600 mt-1">
              إضافة مصروف جديد إلى النظام المالي
            </p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                المعلومات الأساسية
              </CardTitle>
              <CardDescription>
                أدخل المعلومات الأساسية للمصروف
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Title */}
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان المصروف *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="أدخل عنوان المصروف"
                    required
                  />
                </div>

                {/* Type */}
                <div className="space-y-2">
                  <Label htmlFor="type">نوع المصروف *</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => handleInputChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع المصروف" />
                    </SelectTrigger>
                    <SelectContent>
                      {expenseTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="أدخل وصف تفصيلي للمصروف"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                المعلومات المالية
              </CardTitle>
              <CardDescription>
                أدخل المبالغ والتواريخ المالية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Amount */}
                <div className="space-y-2">
                  <Label htmlFor="amount">المبلغ (جنيه) *</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.amount}
                    onChange={(e) => handleInputChange('amount', e.target.value)}
                    placeholder="0.00"
                    required
                  />
                </div>

                {/* Tax Amount */}
                <div className="space-y-2">
                  <Label htmlFor="tax_amount">مبلغ الضريبة (جنيه)</Label>
                  <Input
                    id="tax_amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.tax_amount}
                    onChange={(e) => handleInputChange('tax_amount', e.target.value)}
                    placeholder="0.00"
                  />
                </div>

                {/* Status */}
                <div className="space-y-2">
                  <Label htmlFor="status">الحالة</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleInputChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الحالة" />
                    </SelectTrigger>
                    <SelectContent>
                      {expenseStatuses.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Expense Date */}
                <div className="space-y-2">
                  <Label htmlFor="expense_date">تاريخ المصروف *</Label>
                  <Input
                    id="expense_date"
                    type="date"
                    value={formData.expense_date}
                    onChange={(e) => handleInputChange('expense_date', e.target.value)}
                    required
                  />
                </div>

                {/* Due Date */}
                <div className="space-y-2">
                  <Label htmlFor="due_date">تاريخ الاستحقاق</Label>
                  <Input
                    id="due_date"
                    type="date"
                    value={formData.due_date}
                    onChange={(e) => handleInputChange('due_date', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                الربط والتصنيف
              </CardTitle>
              <CardDescription>
                ربط المصروف بالفئات والمشاريع وأعضاء الفريق
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Category */}
                <div className="space-y-2">
                  <Label htmlFor="category">الفئة</Label>
                  <Select
                    value={formData.category?.toString() || 'none'}
                    onValueChange={(value) => handleInputChange('category', value === 'none' ? null : parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الفئة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">بدون فئة</SelectItem>
                      {categories.map((category: any) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Project */}
                <div className="space-y-2">
                  <Label htmlFor="project">المشروع</Label>
                  <Select
                    value={formData.project?.toString() || 'none'}
                    onValueChange={(value) => handleInputChange('project', value === 'none' ? null : parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر المشروع" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">بدون مشروع</SelectItem>
                      {projects.map((project: any) => (
                        <SelectItem key={project.id} value={project.id.toString()}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Team Member */}
                <div className="space-y-2">
                  <Label htmlFor="team_member">عضو الفريق</Label>
                  <Select
                    value={formData.team_member?.toString() || 'none'}
                    onValueChange={(value) => handleInputChange('team_member', value === 'none' ? null : parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر عضو الفريق" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">بدون عضو فريق</SelectItem>
                      {teamMembers.map((member: any) => (
                        <SelectItem key={member.id} value={member.id.toString()}>
                          {member.first_name} {member.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                معلومات إضافية
              </CardTitle>
              <CardDescription>
                معلومات إضافية عن المصروف والمورد
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Receipt Number */}
                <div className="space-y-2">
                  <Label htmlFor="receipt_number">رقم الإيصال</Label>
                  <Input
                    id="receipt_number"
                    value={formData.receipt_number}
                    onChange={(e) => handleInputChange('receipt_number', e.target.value)}
                    placeholder="أدخل رقم الإيصال"
                  />
                </div>

                {/* Vendor Name */}
                <div className="space-y-2">
                  <Label htmlFor="vendor_name">اسم المورد</Label>
                  <Input
                    id="vendor_name"
                    value={formData.vendor_name}
                    onChange={(e) => handleInputChange('vendor_name', e.target.value)}
                    placeholder="أدخل اسم المورد"
                  />
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">ملاحظات</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="أدخل أي ملاحظات إضافية"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-red-600 hover:bg-red-700"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin ml-2" />
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ المصروف
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </UnifiedLayout>
  );
}
